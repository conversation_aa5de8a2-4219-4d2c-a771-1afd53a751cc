#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to completely skip browser-use integration and revert to working state.
This removes all browser-use dependencies and reverts changes.
"""

import shutil
from pathlib import Path


def remove_browser_use_files():
    """Remove all browser-use related files."""
    files_to_remove = [
        "agents/browser_use_agent.py",
        "config/browser_use_config.py",
        "scripts/install_browser_use.py",
        "scripts/test_browser_use.py",
        "scripts/verify_dependencies.py",
        "scripts/resolve_all_conflicts.py",
        "docs/browser_use_migration.md",
        "BROWSER_USE_REPLACEMENT_SUMMARY.md"
    ]
    
    removed = []
    for file_path in files_to_remove:
        file = Path(file_path)
        if file.exists():
            file.unlink()
            removed.append(str(file))
    
    return removed


def revert_requirements():
    """Remove browser-use from requirements.txt."""
    req_file = Path("requirements.txt")
    if not req_file.exists():
        return False
    
    content = req_file.read_text()
    lines = content.split('\n')
    
    # Remove browser-use related lines
    filtered_lines = []
    for line in lines:
        if not any(pkg in line.lower() for pkg in ['browser-use', 'playwright']):
            filtered_lines.append(line)
    
    req_file.write_text('\n'.join(filtered_lines))
    return True


def revert_orchestrator():
    """Revert orchestrator to use original WebSurfer."""
    orchestrator_file = Path("agents/orchestrator_agent.py")
    if not orchestrator_file.exists():
        return False
    
    content = orchestrator_file.read_text()
    
    # Revert import
    content = content.replace(
        "from agents.browser_use_agent import BrowserUseWebSurfer",
        "from agents.web_surfer import WebSurfer"
    )
    
    # Revert agent initialization
    old_init = '''        # Initialize BrowserUse WebSurfer agent with default configuration
        web_surfer_agent = BrowserUseWebSurfer(
            # Uses default configuration from browser_use_config.py
            # Will automatically detect available API keys and use appropriate LLM
        )'''
    
    new_init = '''        # Initialize WebSurfer agent
        web_surfer_agent = WebSurfer(api_url="http://agentic_browser:8000/api/v1/web/stream")'''
    
    content = content.replace(old_init, new_init)
    
    # Remove cleanup calls
    content = content.replace("await web_surfer_agent.cleanup()", "")
    content = content.replace("""        # Clean up browser session
        await web_surfer_agent.cleanup()
        """, "")
    content = content.replace("""        # Clean up browser session even on error
        try:
            await web_surfer_agent.cleanup()
        except:
            pass  # Ignore cleanup errors
            """, "")
    
    orchestrator_file.write_text(content)
    return True


def revert_docker_compose():
    """Revert docker-compose to include agentic_browser dependency."""
    compose_file = Path("../docker-compose.yaml")
    if not compose_file.exists():
        return False
    
    content = compose_file.read_text()
    
    # Restore agentic_browser dependency
    content = content.replace(
        """    depends_on:
      - cortex_on
      # Removed agentic_browser dependency - cortex_on now uses browser-use""",
        """    depends_on:
      - cortex_on
      - agentic_browser"""
    )
    
    # Remove profile from agentic_browser
    content = content.replace(
        """  # Legacy browser service - now optional since cortex_on uses browser-use
  agentic_browser:
    build:
      context: ./ta-browser
      dockerfile: Dockerfile
    volumes:
      - ./ta-browser:/app
    env_file:
      - .env
    restart: always
    ports:
      - "8000:8000"
    profiles:
      - legacy  # Use 'docker-compose --profile legacy up' to include this service""",
        """  agentic_browser:
    build:
      context: ./ta-browser
      dockerfile: Dockerfile
    volumes:
      - ./ta-browser:/app
    env_file:
      - .env
    restart: always
    ports:
      - "8000:8000" """
    )
    
    compose_file.write_text(content)
    return True


def main():
    """Main function to skip browser-use integration."""
    print("🔄 Skipping Browser-Use Integration - Reverting to Working State")
    print("=" * 70)
    
    # Check if we're in the right directory
    if not Path("requirements.txt").exists():
        print("❌ Please run this script from the cortex_on directory")
        return
    
    # Remove browser-use files
    print("🗑️  Removing browser-use files...")
    removed_files = remove_browser_use_files()
    for file in removed_files:
        print(f"  ✅ Removed: {file}")
    
    # Revert requirements.txt
    print("\n📝 Reverting requirements.txt...")
    if revert_requirements():
        print("  ✅ Removed browser-use dependencies")
    
    # Revert orchestrator
    print("\n🔧 Reverting orchestrator agent...")
    if revert_orchestrator():
        print("  ✅ Restored original WebSurfer integration")
    
    # Revert docker-compose
    print("\n🐳 Reverting docker-compose...")
    if revert_docker_compose():
        print("  ✅ Restored agentic_browser dependency")
    
    print("\n" + "=" * 70)
    print("🎉 Successfully reverted to working state!")
    print("\n💡 Your system is now back to the original configuration:")
    print("  • Uses original WebSurfer agent")
    print("  • Connects to agentic_browser service")
    print("  • No browser-use dependencies")
    print("  • All original functionality preserved")
    
    print("\n🚀 Ready to build:")
    print("  cd ..")
    print("  docker-compose up --build")
    
    print("\n📋 What was reverted:")
    print("  • Removed browser-use integration files")
    print("  • Restored original requirements.txt")
    print("  • Reverted orchestrator to use WebSurfer")
    print("  • Restored docker-compose dependencies")


if __name__ == "__main__":
    main()

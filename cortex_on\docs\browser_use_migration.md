# Browser-Use Migration Guide

This document explains the migration from the previous web surfer implementation to the new browser-use framework.

## Overview

The web surfer agent has been upgraded from a custom API-based solution to use the modern [browser-use](https://github.com/browser-use/browser-use) framework. This provides better AI-driven browser automation with improved reliability and capabilities.

## What Changed

### Before (Old Implementation)
- **WebSurfer Agent** (`agents/web_surfer.py`): Made HTTP API calls to `http://agentic_browser:8000/api/v1/web/stream`
- **External Dependency**: Required a separate browser automation service
- **Limited AI Integration**: Basic instruction-following without intelligent reasoning
- **Manual Error Handling**: Required custom retry logic and error management

### After (New Implementation)
- **BrowserUseWebSurfer Agent** (`agents/browser_use_agent.py`): Direct AI-to-browser interaction
- **Self-Contained**: No external API dependencies for browser automation
- **Intelligent Automation**: AI can reason about web pages and adapt to different layouts
- **Vision Capabilities**: Can "see" web pages and make decisions based on visual content
- **Better Error Recovery**: Built-in retry mechanisms and intelligent error handling

## Key Benefits

1. **Simplified Architecture**: Eliminates the need for external browser API services
2. **Better AI Integration**: Direct LLM-to-browser communication with reasoning capabilities
3. **Vision Support**: AI can analyze screenshots and make visual decisions
4. **Improved Reliability**: Better error handling and retry mechanisms
5. **Modern Framework**: Actively maintained with regular updates
6. **Flexible LLM Support**: Works with OpenAI, Anthropic, and other providers

## Installation

### 1. Install Dependencies

```bash
# Install browser-use
pip install browser-use

# Install Playwright browser
playwright install chromium --with-deps --no-shell
```

Or use the automated installation script:

```bash
python scripts/install_browser_use.py
```

### 2. Configure API Keys

Add your LLM API keys to the `.env` file:

```bash
# At least one of these is required
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Optional: Enable debug logging
BROWSER_USE_LOGGING_LEVEL=debug
```

### 3. Test the Installation

```bash
python scripts/test_browser_use.py
```

## Configuration

The new implementation uses `config/browser_use_config.py` for configuration:

### Default Settings
- **LLM Provider**: Anthropic (Claude 3.5 Sonnet)
- **Fallback**: OpenAI (GPT-4o)
- **Browser Mode**: Headless (for production)
- **Vision**: Enabled
- **Max Steps**: 20 per task

### Environment-Specific Settings

```python
from config.browser_use_config import BrowserUseConfig

# Development settings (shows browser window)
dev_config = BrowserUseConfig.get_recommended_settings("development")

# Production settings (headless)
prod_config = BrowserUseConfig.get_recommended_settings("production")

# Testing settings (fast, minimal features)
test_config = BrowserUseConfig.get_recommended_settings("testing")
```

## Usage

### Basic Usage

The orchestrator agent automatically uses the new browser-use implementation:

```python
# This now uses BrowserUseWebSurfer internally
await web_surfer_task("Go to example.com and extract the main heading")
```

### Advanced Usage

For direct usage:

```python
from agents.browser_use_agent import BrowserUseWebSurfer

# Create agent with default configuration
agent = BrowserUseWebSurfer()

# Or with custom configuration
agent = BrowserUseWebSurfer(
    llm_provider="openai",
    model="gpt-4o",
    headless=False,  # Show browser window
    use_vision=True
)

# Use the agent
success, result, messages = await agent.generate_reply(
    instruction="Navigate to example.com and get the page title",
    websocket=websocket,
    stream_output=stream_output
)

# Clean up
await agent.cleanup()
```

## Migration Checklist

- [ ] Install browser-use: `pip install browser-use`
- [ ] Install Playwright browser: `playwright install chromium --with-deps --no-shell`
- [ ] Add API keys to `.env` file (OPENAI_API_KEY or ANTHROPIC_API_KEY)
- [ ] Test installation: `python scripts/test_browser_use.py`
- [ ] Update any direct imports from `agents.web_surfer` to `agents.browser_use_agent`
- [ ] Remove external browser API service dependencies (if any)
- [ ] Update deployment scripts to include new dependencies

## Troubleshooting

### Common Issues

1. **Missing API Keys**
   ```
   Error: Missing API key for anthropic
   ```
   **Solution**: Add `ANTHROPIC_API_KEY` or `OPENAI_API_KEY` to your `.env` file

2. **Browser Not Installed**
   ```
   Error: Chromium distribution not found
   ```
   **Solution**: Run `playwright install chromium --with-deps --no-shell`

3. **Import Errors**
   ```
   ImportError: No module named 'browser_use'
   ```
   **Solution**: Install browser-use with `pip install browser-use`

### Debug Mode

Enable detailed logging for troubleshooting:

```bash
# In .env file
BROWSER_USE_LOGGING_LEVEL=debug
```

### Performance Tuning

For better performance in production:

```python
# Disable vision for faster execution (if visual analysis not needed)
agent = BrowserUseWebSurfer(use_vision=False)

# Reduce max steps for simpler tasks
agent = BrowserUseWebSurfer()
agent.max_steps = 10
```

## Backward Compatibility

The new implementation maintains backward compatibility:

- The `WebSurfer` class is aliased to `BrowserUseWebSurfer`
- The `web_surfer_task` function in the orchestrator works unchanged
- All existing task instructions should work without modification

## Support

For issues related to:
- **Browser-use framework**: Check the [official documentation](https://github.com/browser-use/browser-use)
- **Integration issues**: Review this migration guide and test scripts
- **Configuration problems**: Check `config/browser_use_config.py`

## Future Enhancements

The new browser-use integration enables future enhancements:

1. **Multi-tab Support**: Handle multiple browser tabs simultaneously
2. **Session Persistence**: Maintain login sessions across tasks
3. **Advanced Vision**: Better screenshot analysis and visual reasoning
4. **Custom Actions**: Add domain-specific browser automation functions
5. **Performance Monitoring**: Track browser automation metrics and success rates

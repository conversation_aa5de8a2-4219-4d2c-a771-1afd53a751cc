# TheCortexOn

CortexON is an advanced AI orchestration system that manages multiple specialized agents to solve complex tasks.

## 🚀 New: Browser-Use Integration

CortexON now uses the modern [browser-use](https://github.com/browser-use/browser-use) framework for intelligent web automation. This provides:

- **AI-Powered Browser Automation**: Direct LLM-to-browser interaction with reasoning capabilities
- **Vision Support**: AI can analyze web pages visually and make intelligent decisions
- **Better Reliability**: Improved error handling and retry mechanisms
- **No External Dependencies**: Self-contained browser automation without external APIs

### Quick Setup for Browser-Use

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   pip install browser-use
   playwright install chromium --with-deps --no-shell
   ```

2. **Configure API Keys** (add to `.env`):
   ```bash
   # At least one required
   OPENAI_API_KEY=your_openai_api_key_here
   ANTHROPIC_API_KEY=your_anthropic_api_key_here
   ```

3. **Test Installation**:
   ```bash
   python scripts/test_browser_use.py
   ```

For detailed migration information, see [`docs/browser_use_migration.md`](docs/browser_use_migration.md).

## Installation & Setup

- Install dependencies using `pip install -r requirements.txt`
- Configure `.env` (using example `.env.copy`)
- Either run `python -m src.main` in root folder
- Or run `uvicorn --reload --access-log --host 0.0.0.0 --port 8001 src.main:app` to use with frontend

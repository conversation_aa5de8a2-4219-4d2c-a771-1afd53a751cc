"""
Configuration settings for browser-use integration.
"""

import os
from typing import Dict, Any, Optional
from dotenv import load_dotenv

load_dotenv()


class BrowserUseConfig:
    """Configuration class for browser-use settings."""
    
    # LLM Configuration
    DEFAULT_LLM_PROVIDER = "anthropic"  # or "openai"
    DEFAULT_OPENAI_MODEL = "gpt-4o"
    DEFAULT_ANTHROPIC_MODEL = "claude-3-5-sonnet-20240620"
    
    # Browser Configuration
    DEFAULT_HEADLESS = True
    DEFAULT_USE_VISION = True
    DEFAULT_VIEWPORT = {"width": 1280, "height": 720}
    DEFAULT_MAX_STEPS = 20
    
    # Timeout Configuration
    DEFAULT_TIMEOUT = 30000  # 30 seconds in milliseconds
    DEFAULT_NAVIGATION_TIMEOUT = 60000  # 60 seconds for navigation
    
    # User Agent
    DEFAULT_USER_AGENT = (
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
        "(KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    )
    
    @classmethod
    def get_llm_config(cls, provider: Optional[str] = None) -> Dict[str, Any]:
        """
        Get LLM configuration based on provider.
        
        Args:
            provider: LLM provider ("openai" or "anthropic")
            
        Returns:
            Dictionary with LLM configuration
        """
        provider = provider or cls.DEFAULT_LLM_PROVIDER
        
        if provider.lower() == "openai":
            return {
                "provider": "openai",
                "model": cls.DEFAULT_OPENAI_MODEL,
                "api_key": os.getenv("OPENAI_API_KEY"),
                "required_env": "OPENAI_API_KEY"
            }
        elif provider.lower() == "anthropic":
            return {
                "provider": "anthropic", 
                "model": cls.DEFAULT_ANTHROPIC_MODEL,
                "api_key": os.getenv("ANTHROPIC_API_KEY"),
                "required_env": "ANTHROPIC_API_KEY"
            }
        else:
            raise ValueError(f"Unsupported LLM provider: {provider}")
    
    @classmethod
    def get_browser_config(cls, headless: Optional[bool] = None) -> Dict[str, Any]:
        """
        Get browser configuration.
        
        Args:
            headless: Whether to run browser in headless mode
            
        Returns:
            Dictionary with browser configuration
        """
        return {
            "headless": headless if headless is not None else cls.DEFAULT_HEADLESS,
            "viewport": cls.DEFAULT_VIEWPORT,
            "user_agent": cls.DEFAULT_USER_AGENT,
            "timeout": cls.DEFAULT_TIMEOUT,
            "navigation_timeout": cls.DEFAULT_NAVIGATION_TIMEOUT,
            "use_vision": cls.DEFAULT_USE_VISION,
            "max_steps": cls.DEFAULT_MAX_STEPS
        }
    
    @classmethod
    def validate_environment(cls, provider: Optional[str] = None) -> bool:
        """
        Validate that required environment variables are set.
        
        Args:
            provider: LLM provider to validate
            
        Returns:
            True if environment is valid, False otherwise
        """
        llm_config = cls.get_llm_config(provider)
        api_key = llm_config.get("api_key")
        
        if not api_key:
            print(f"Warning: {llm_config['required_env']} environment variable not set")
            return False
            
        return True
    
    @classmethod
    def get_recommended_settings(cls, environment: str = "production") -> Dict[str, Any]:
        """
        Get recommended settings for different environments.
        
        Args:
            environment: Environment type ("development", "production", "testing")
            
        Returns:
            Dictionary with recommended settings
        """
        base_settings = {
            "llm_provider": cls.DEFAULT_LLM_PROVIDER,
            "use_vision": cls.DEFAULT_USE_VISION,
            "max_steps": cls.DEFAULT_MAX_STEPS
        }
        
        if environment == "development":
            base_settings.update({
                "headless": False,  # Show browser for debugging
                "save_conversation_path": "logs/browser_conversations",
                "generate_gif": True,  # Generate GIFs for debugging
                "viewport": {"width": 1280, "height": 720}
            })
        elif environment == "production":
            base_settings.update({
                "headless": True,  # Run headless in production
                "save_conversation_path": None,  # Don't save conversations
                "generate_gif": False,  # Don't generate GIFs
                "viewport": {"width": 1920, "height": 1080}  # Larger viewport
            })
        elif environment == "testing":
            base_settings.update({
                "headless": True,  # Run headless for tests
                "max_steps": 10,  # Limit steps for faster tests
                "use_vision": False,  # Disable vision for faster tests
                "viewport": {"width": 1024, "height": 768}
            })
        
        return base_settings


# Environment-specific configurations
DEVELOPMENT_CONFIG = BrowserUseConfig.get_recommended_settings("development")
PRODUCTION_CONFIG = BrowserUseConfig.get_recommended_settings("production")
TESTING_CONFIG = BrowserUseConfig.get_recommended_settings("testing")

# Default configuration (production)
DEFAULT_CONFIG = PRODUCTION_CONFIG

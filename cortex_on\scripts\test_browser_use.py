#!/usr/bin/env python3
"""
Test script for browser-use integration.
This script tests the BrowserUseWebSurfer agent functionality.
"""

import asyncio
import json
import sys
from pathlib import Path

# Add the parent directory to the path so we can import our modules
sys.path.append(str(Path(__file__).parent.parent))

from agents.browser_use_agent import BrowserUseWebSurfer
from utils.stream_response_format import StreamResponse
from config.browser_use_config import BrowserUseConfig


class MockWebSocket:
    """Mock WebSocket for testing."""
    
    def __init__(self):
        self.messages = []
    
    async def send_text(self, message):
        """Mock send_text method."""
        self.messages.append(message)
        print(f"📤 WebSocket message: {json.loads(message)['steps'][-1] if json.loads(message)['steps'] else 'No steps'}")


async def test_simple_navigation():
    """Test simple web navigation."""
    print("🧪 Testing simple web navigation...")
    
    # Create mock websocket and stream response
    websocket = MockWebSocket()
    stream_output = StreamResponse(
        agent_name="Test Browser Agent",
        instructions="Navigate to example.com and get the page title",
        steps=[],
        output="",
        status_code=0,
        live_url=None
    )
    
    # Create browser agent
    try:
        agent = BrowserUseWebSurfer(
            llm_provider="anthropic",  # Try Anthropic first
            headless=True,  # Run headless for testing
            use_vision=False  # Disable vision for faster testing
        )
    except ValueError as e:
        print(f"❌ Failed to create agent with Anthropic: {e}")
        try:
            agent = BrowserUseWebSurfer(
                llm_provider="openai",  # Fallback to OpenAI
                headless=True,
                use_vision=False
            )
        except ValueError as e:
            print(f"❌ Failed to create agent with OpenAI: {e}")
            print("💡 Please ensure you have either ANTHROPIC_API_KEY or OPENAI_API_KEY set in your .env file")
            return False
    
    # Test the agent
    try:
        success, message, _ = await agent.generate_reply(
            instruction="Go to https://example.com and tell me what the main heading says",
            websocket=websocket,
            stream_output=stream_output
        )
        
        if success:
            print(f"✅ Test passed! Result: {message[:100]}...")
        else:
            print(f"❌ Test failed: {message}")
        
        # Cleanup
        await agent.cleanup()
        return success
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        await agent.cleanup()
        return False


async def test_configuration():
    """Test configuration loading."""
    print("🧪 Testing configuration...")
    
    # Test LLM configuration
    try:
        openai_config = BrowserUseConfig.get_llm_config("openai")
        anthropic_config = BrowserUseConfig.get_llm_config("anthropic")
        print(f"✅ OpenAI config: {openai_config['model']}")
        print(f"✅ Anthropic config: {anthropic_config['model']}")
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False
    
    # Test browser configuration
    try:
        browser_config = BrowserUseConfig.get_browser_config()
        print(f"✅ Browser config: {browser_config['viewport']}")
    except Exception as e:
        print(f"❌ Browser configuration test failed: {e}")
        return False
    
    # Test environment validation
    openai_valid = BrowserUseConfig.validate_environment("openai")
    anthropic_valid = BrowserUseConfig.validate_environment("anthropic")
    
    if openai_valid:
        print("✅ OpenAI environment is valid")
    else:
        print("⚠️  OpenAI environment is not configured")
    
    if anthropic_valid:
        print("✅ Anthropic environment is valid")
    else:
        print("⚠️  Anthropic environment is not configured")
    
    if not openai_valid and not anthropic_valid:
        print("❌ No valid LLM environment found")
        return False
    
    return True


async def main():
    """Main test function."""
    print("🚀 Browser-Use Integration Test")
    print("=" * 50)
    
    # Test configuration
    config_success = await test_configuration()
    if not config_success:
        print("\n❌ Configuration tests failed")
        return
    
    print("\n" + "=" * 50)
    
    # Test simple navigation
    nav_success = await test_simple_navigation()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"Configuration: {'✅ PASS' if config_success else '❌ FAIL'}")
    print(f"Navigation: {'✅ PASS' if nav_success else '❌ FAIL'}")
    
    if config_success and nav_success:
        print("\n🎉 All tests passed! Browser-use integration is working correctly.")
    else:
        print("\n❌ Some tests failed. Please check your configuration and API keys.")
        print("\n💡 Troubleshooting tips:")
        print("1. Ensure you have either ANTHROPIC_API_KEY or OPENAI_API_KEY in your .env file")
        print("2. Run 'playwright install chromium --with-deps --no-shell' to install browser")
        print("3. Check that browser-use is installed: 'pip install browser-use'")


if __name__ == "__main__":
    asyncio.run(main())

#!/usr/bin/env python3
"""
Comprehensive script to resolve all browser-use dependency conflicts.
This script will automatically fix all known conflicts in requirements.txt.
"""

import re
import subprocess
import sys
from pathlib import Path


def get_browser_use_requirements():
    """Get the actual requirements for browser-use from PyPI."""
    try:
        result = subprocess.run([
            sys.executable, "-m", "pip", "show", "browser-use"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            # Parse the requirements from pip show output
            for line in result.stdout.split('\n'):
                if line.startswith('Requires:'):
                    requires = line.replace('Requires:', '').strip()
                    return requires.split(', ') if requires else []
        
        # Fallback to known requirements if pip show fails
        return [
            'pydantic>=2.11.5',
            'playwright>=1.52.0', 
            'openai>=1.81.0',
            'anthropic>=0.40.0'
        ]
        
    except Exception:
        # Fallback requirements based on error messages we've seen
        return [
            'pydantic>=2.11.5',
            'playwright>=1.52.0', 
            'openai>=1.81.0',
            'anthropic>=0.40.0'
        ]


def fix_all_conflicts():
    """Fix all known browser-use dependency conflicts."""
    req_file = Path("requirements.txt")
    if not req_file.exists():
        print("❌ requirements.txt not found")
        return False
    
    content = req_file.read_text()
    original_content = content
    
    # Known conflicts and their fixes
    fixes = [
        # Pydantic conflict
        (r'pydantic==2\.10\.4', 'pydantic>=2.11.5'),
        (r'pydantic_core==[\d\.]+', 'pydantic_core>=2.27.2'),
        
        # Playwright conflict
        (r'playwright==1\.49\.1', 'playwright>=1.52.0'),
        
        # OpenAI conflict
        (r'openai==1\.58\.1', 'openai>=1.81.0'),
        
        # Anthropic - might need updating too
        (r'anthropic==0\.42\.0', 'anthropic>=0.42.0'),
        
        # Browser-use version
        (r'browser-use==[\d\.]+', 'browser-use==0.5.4'),
    ]
    
    changes_made = []
    
    for pattern, replacement in fixes:
        if re.search(pattern, content):
            old_match = re.search(pattern, content)
            if old_match:
                old_value = old_match.group(0)
                content = re.sub(pattern, replacement, content)
                changes_made.append(f"{old_value} → {replacement}")
    
    # Add browser-use if not present
    if 'browser-use==' not in content:
        content += '\nbrowser-use==0.5.4\n'
        changes_made.append("Added browser-use==0.5.4")
    
    # Add playwright if not present
    if 'playwright' not in content:
        content += 'playwright>=1.52.0\n'
        changes_made.append("Added playwright>=1.52.0")
    
    if content != original_content:
        req_file.write_text(content)
        print("✅ Fixed requirements.txt with the following changes:")
        for change in changes_made:
            print(f"  • {change}")
        return True
    else:
        print("ℹ️  No changes needed in requirements.txt")
        return True


def test_resolution():
    """Test if browser-use can be installed with current requirements."""
    print("\n🧪 Testing dependency resolution...")
    
    try:
        # Test browser-use installation
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", 
            "browser-use==0.5.4",
            "--dry-run"
        ], capture_output=True, text=True, check=True)
        
        print("✅ browser-use==0.5.4 can be installed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ browser-use installation test failed:")
        print(f"Error: {e.stderr}")
        
        # Try to extract specific conflicts from error message
        if "depends on" in e.stderr and "you require" in e.stderr:
            lines = e.stderr.split('\n')
            for line in lines:
                if "depends on" in line and "you require" in line:
                    print(f"Conflict: {line.strip()}")
        
        return False


def main():
    """Main function to resolve all conflicts."""
    print("🔧 Comprehensive Browser-Use Dependency Resolver")
    print("=" * 60)
    
    # Check current directory
    if not Path("requirements.txt").exists():
        print("❌ Please run this script from the cortex_on directory")
        sys.exit(1)
    
    # Get browser-use requirements
    print("📋 Getting browser-use requirements...")
    requirements = get_browser_use_requirements()
    print(f"Browser-use requires: {', '.join(requirements)}")
    
    # Fix all conflicts
    print("\n🔧 Fixing dependency conflicts...")
    fix_success = fix_all_conflicts()
    
    # Test resolution
    test_success = test_resolution()
    
    print("\n" + "=" * 60)
    print("📊 Resolution Results:")
    print(f"Requirements fixed: {'✅ SUCCESS' if fix_success else '❌ FAILED'}")
    print(f"Dependency test: {'✅ SUCCESS' if test_success else '❌ FAILED'}")
    
    if fix_success and test_success:
        print("\n🎉 All dependency conflicts resolved!")
        print("\n💡 Ready to build Docker:")
        print("cd ..")  # Go back to project root
        print("docker-compose up --build")
    else:
        print("\n⚠️  Some issues remain. Check the errors above.")
        print("\n💡 You may need to:")
        print("1. Update more package versions manually")
        print("2. Check for additional conflicts in the error messages")
        print("3. Consider using a different browser-use version")
    
    print("\n📚 For more help:")
    print("- Run: python scripts/fix_docker_build.py")
    print("- Run: python scripts/verify_dependencies.py")


if __name__ == "__main__":
    main()

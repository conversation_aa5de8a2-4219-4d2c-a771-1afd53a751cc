FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install uv
# Install system dependencies for browser-use and Playwright
RUN apt-get update && apt-get install -y \
    build-essential \
    cmake \
    g++ \
    wget \
    gnupg \
    ca-certificates \
    fonts-liberation \
    libasound2 \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libatspi2.0-0 \
    libcups2 \
    libdbus-1-3 \
    libdrm2 \
    libgtk-3-0 \
    libnspr4 \
    libnss3 \
    libwayland-client0 \
    libx11-6 \
    libx11-xcb1 \
    libxcb1 \
    libxcomposite1 \
    libxdamage1 \
    libxext6 \
    libxfixes3 \
    libxrandr2 \
    libxss1 \
    libxtst6 \
    && rm -rf /var/lib/apt/lists/*

RUN export PYTHONPATH=/app

# Install Python packages
RUN uv pip install --system --no-cache-dir -r requirements.txt

# Install Playwright browser for browser-use
RUN playwright install chromium --with-deps --no-shell

COPY . .

EXPOSE 8081

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8081"]
#!/usr/bin/env python3
"""
Quick fix script for Docker build issues with browser-use.
This script helps resolve common Docker build problems.
"""

import subprocess
import sys
import os
from pathlib import Path


def check_requirements_file():
    """Check if requirements.txt has the correct browser-use version."""
    req_file = Path("requirements.txt")
    if not req_file.exists():
        print("❌ requirements.txt not found")
        return False
    
    content = req_file.read_text()
    if "browser-use==0.5.4" in content:
        print("✅ requirements.txt has correct browser-use version (0.5.4)")
        return True
    elif "browser-use==" in content:
        print("⚠️  requirements.txt has browser-use but wrong version")
        return False
    else:
        print("❌ browser-use not found in requirements.txt")
        return False


def fix_requirements():
    """Fix the requirements.txt file with correct browser-use version."""
    req_file = Path("requirements.txt")
    if not req_file.exists():
        print("❌ requirements.txt not found")
        return False
    
    content = req_file.read_text()
    
    # Replace any existing browser-use version with the correct one
    import re
    content = re.sub(r'browser-use==[\d\.]+', 'browser-use==0.5.4', content)
    
    # If browser-use wasn't found, add it
    if 'browser-use==' not in content:
        content += '\nbrowser-use==0.5.4\nplaywright==1.49.1\n'
    
    req_file.write_text(content)
    print("✅ Fixed requirements.txt with browser-use==0.5.4")
    return True


def check_python_version_in_dockerfile():
    """Check if Dockerfile uses Python 3.11+ (required by browser-use)."""
    dockerfile = Path("Dockerfile")
    if not dockerfile.exists():
        print("⚠️  Dockerfile not found")
        return True  # Not an error if no Dockerfile

    content = dockerfile.read_text()
    if "python:3.11" in content or "python:3.12" in content or "python:3.13" in content:
        print("✅ Dockerfile uses compatible Python version (3.11+)")
        return True
    elif "python:3.10" in content:
        print("❌ Dockerfile uses Python 3.10, but browser-use requires Python 3.11+")
        return False
    else:
        print("⚠️  Could not determine Python version in Dockerfile")
        return True


def check_playwright_in_dockerfile():
    """Check if Dockerfile installs Playwright browser."""
    dockerfile = Path("Dockerfile")
    if not dockerfile.exists():
        return True

    content = dockerfile.read_text()
    if "playwright install" in content:
        print("✅ Dockerfile installs Playwright browser")
        return True
    else:
        print("⚠️  Dockerfile doesn't install Playwright browser")
        return False


def suggest_dockerfile_fix():
    """Suggest fixes for Dockerfile."""
    print("\n💡 Dockerfile suggestions:")
    print("1. Use Python 3.11 or later:")
    print("   FROM python:3.11-slim")
    print("\n2. Install system dependencies for Playwright:")
    print("   RUN apt-get update && apt-get install -y \\")
    print("       wget gnupg ca-certificates \\")
    print("       fonts-liberation libasound2 libatk-bridge2.0-0 \\")
    print("       && rm -rf /var/lib/apt/lists/*")
    print("\n3. Install Playwright browser after pip install:")
    print("   RUN playwright install chromium --with-deps --no-shell")


def test_browser_use_installation():
    """Test if browser-use can be installed locally."""
    print("\n🧪 Testing browser-use installation locally...")
    try:
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "browser-use==0.5.4", "--dry-run"
        ], capture_output=True, text=True, check=True)
        print("✅ browser-use==0.5.4 can be installed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to test browser-use installation: {e.stderr}")
        return False


def main():
    """Main function to diagnose and fix Docker build issues."""
    print("🔧 Docker Build Fix Script for Browser-Use")
    print("=" * 50)
    
    # Check current directory
    if not Path("requirements.txt").exists():
        print("❌ Please run this script from the cortex_on directory")
        sys.exit(1)
    
    # Check requirements.txt
    req_ok = check_requirements_file()
    if not req_ok:
        print("\n🔧 Fixing requirements.txt...")
        fix_requirements()
    
    # Check Dockerfile Python version
    dockerfile_ok = check_python_version_in_dockerfile()

    # Check Playwright installation in Dockerfile
    playwright_ok = check_playwright_in_dockerfile()

    # Test browser-use installation
    install_ok = test_browser_use_installation()

    print("\n" + "=" * 50)
    print("📊 Diagnosis Results:")
    print(f"Requirements.txt: {'✅ OK' if req_ok else '🔧 FIXED'}")
    print(f"Dockerfile Python: {'✅ OK' if dockerfile_ok else '❌ NEEDS FIX'}")
    print(f"Dockerfile Playwright: {'✅ OK' if playwright_ok else '⚠️  MISSING'}")
    print(f"Browser-use install: {'✅ OK' if install_ok else '❌ ISSUE'}")

    if not dockerfile_ok or not playwright_ok:
        suggest_dockerfile_fix()
    
    if req_ok and dockerfile_ok and playwright_ok and install_ok:
        print("\n🎉 All checks passed! Docker build should work now.")
        print("\n💡 If you still have issues, try:")
        print("1. Clear Docker cache: docker system prune -a")
        print("2. Rebuild without cache: docker-compose build --no-cache")
    else:
        print("\n⚠️  Some issues found. Please fix them before building Docker image.")
    
    print("\n📚 For more help, see:")
    print("- docs/browser_use_migration.md")
    print("- scripts/install_browser_use.py")


if __name__ == "__main__":
    main()

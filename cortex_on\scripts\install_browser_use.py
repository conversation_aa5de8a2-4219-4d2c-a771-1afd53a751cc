#!/usr/bin/env python3
"""
Installation script for browser-use dependencies.
This script installs browser-use and sets up Playwright browsers.
"""

import subprocess
import sys
import os
from pathlib import Path


def run_command(command, description):
    """Run a command and handle errors."""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        if result.stdout:
            print(f"Output: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed")
        print(f"Error: {e.stderr}")
        return False


def check_python_version():
    """Check if Python version is 3.11 or higher."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 11):
        print(f"❌ Python 3.11+ required, but found {version.major}.{version.minor}")
        return False
    print(f"✅ Python {version.major}.{version.minor} is compatible")
    return True


def install_browser_use():
    """Install browser-use package."""
    commands = [
        ("pip install browser-use", "Installing browser-use"),
        ("playwright install chromium --with-deps --no-shell", "Installing Playwright Chromium browser"),
    ]
    
    for command, description in commands:
        if not run_command(command, description):
            return False
    return True


def verify_installation():
    """Verify that browser-use is properly installed."""
    try:
        import browser_use
        print(f"✅ browser-use {getattr(browser_use, '__version__', 'unknown')} installed successfully")
        return True
    except ImportError as e:
        print(f"❌ browser-use installation verification failed: {e}")
        return False


def create_env_template():
    """Create a .env template file if it doesn't exist."""
    env_file = Path(".env")
    env_template = """# Browser-Use Configuration
# Add your API keys below (at least one is required)

# OpenAI API Key (for GPT models)
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic API Key (for Claude models)  
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Optional: Azure OpenAI Configuration
# AZURE_OPENAI_ENDPOINT=https://your-endpoint.openai.azure.com/
# AZURE_OPENAI_API_KEY=your_azure_api_key_here

# Optional: Google API Key (for Gemini models)
# GOOGLE_API_KEY=your_google_api_key_here

# Browser-Use Logging Level (for development)
BROWSER_USE_LOGGING_LEVEL=info
"""
    
    if not env_file.exists():
        with open(env_file, 'w') as f:
            f.write(env_template)
        print(f"✅ Created .env template file at {env_file.absolute()}")
        print("📝 Please edit .env and add your API keys")
    else:
        print(f"ℹ️  .env file already exists at {env_file.absolute()}")


def main():
    """Main installation function."""
    print("🚀 Browser-Use Installation Script")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install browser-use
    if not install_browser_use():
        print("\n❌ Installation failed. Please check the errors above.")
        sys.exit(1)
    
    # Verify installation
    if not verify_installation():
        print("\n❌ Installation verification failed.")
        sys.exit(1)
    
    # Create .env template
    create_env_template()
    
    print("\n🎉 Browser-Use installation completed successfully!")
    print("\n📋 Next steps:")
    print("1. Edit the .env file and add your API keys")
    print("2. Test the installation by running a simple browser-use script")
    print("3. The web surfer agent will now use browser-use instead of the external API")
    
    print("\n💡 Tips:")
    print("- Use OPENAI_API_KEY for GPT models (gpt-4o, gpt-4o-mini)")
    print("- Use ANTHROPIC_API_KEY for Claude models (claude-3-5-sonnet)")
    print("- Set BROWSER_USE_LOGGING_LEVEL=debug for detailed logging during development")


if __name__ == "__main__":
    main()

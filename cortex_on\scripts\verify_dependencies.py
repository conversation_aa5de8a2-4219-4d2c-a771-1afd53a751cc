#!/usr/bin/env python3
"""
Quick verification script to check if all browser-use dependencies are compatible.
"""

import subprocess
import sys
from pathlib import Path


def check_dependency_compatibility():
    """Check if browser-use dependencies are compatible."""
    print("🔍 Checking browser-use dependency compatibility...")
    
    try:
        # Test if browser-use can be resolved with current requirements
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", 
            "browser-use==0.5.4", 
            "playwright>=1.52.0",
            "pydantic>=2.11.5",
            "--dry-run"
        ], capture_output=True, text=True, check=True)
        
        print("✅ All browser-use dependencies are compatible!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Dependency compatibility check failed:")
        print(f"Error: {e.stderr}")
        return False


def check_requirements_file():
    """Check if requirements.txt has the correct versions."""
    req_file = Path("requirements.txt")
    if not req_file.exists():
        print("❌ requirements.txt not found")
        return False
    
    content = req_file.read_text()
    
    checks = [
        ("browser-use==0.5.4", "browser-use version"),
        ("pydantic>=2.11.5", "pydantic version"),
        ("playwright>=1.52.0", "playwright version"),
    ]
    
    all_good = True
    for check, description in checks:
        if check in content:
            print(f"✅ {description} is correct")
        else:
            print(f"❌ {description} is incorrect or missing")
            all_good = False
    
    return all_good


def main():
    """Main verification function."""
    print("🧪 Browser-Use Dependency Verification")
    print("=" * 50)
    
    # Check requirements.txt
    req_ok = check_requirements_file()
    
    print("\n" + "-" * 50)
    
    # Check dependency compatibility
    compat_ok = check_dependency_compatibility()
    
    print("\n" + "=" * 50)
    print("📊 Verification Results:")
    print(f"Requirements file: {'✅ PASS' if req_ok else '❌ FAIL'}")
    print(f"Dependency compatibility: {'✅ PASS' if compat_ok else '❌ FAIL'}")
    
    if req_ok and compat_ok:
        print("\n🎉 All checks passed! Docker build should work now.")
        print("\n💡 Ready to build:")
        print("docker-compose build")
    else:
        print("\n❌ Some checks failed. Please fix the issues above.")
        print("\n💡 Run the fix script:")
        print("python scripts/fix_docker_build.py")


if __name__ == "__main__":
    main()

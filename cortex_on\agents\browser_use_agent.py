# Standard library imports
import json
import os
import uuid
import asyncio
from dataclasses import asdict
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

# Third-party imports
from dotenv import load_dotenv
from fastapi import WebSocket
import logfire
from pydantic_ai.messages import (
    <PERSON>rgsJson,
    ModelRequest,
    ModelResponse,
    ToolCallPart,
    ToolReturnPart,
    UserPromptPart,
)

# Browser-use imports
from browser_use import Agent as BrowserUseAgent, BrowserSession
from browser_use.llm import ChatOpenAI, ChatAnthropic

# Local application imports
from utils.stream_response_format import StreamResponse
from config.browser_use_config import BrowserUseConfig

load_dotenv()


class BrowserUseWebSurfer:
    """
    A web surfer agent that uses browser-use for AI-driven browser automation.
    This replaces the previous WebSurfer that relied on external API calls.
    """
    
    def __init__(self,
                 llm_provider: Optional[str] = None,
                 model: Optional[str] = None,
                 headless: Optional[bool] = None,
                 use_vision: Optional[bool] = None):
        """
        Initialize the BrowserUse web surfer agent.

        Args:
            llm_provider: LLM provider ("openai" or "anthropic"), defaults to config
            model: Model name, defaults to config based on provider
            headless: Whether to run browser in headless mode, defaults to config
            use_vision: Whether to enable vision capabilities, defaults to config
        """
        self.name = "Browser Use Web Surfer Agent"
        self.description = "An AI-powered web surfer using browser-use for intelligent browser automation"
        self.websocket: Optional[WebSocket] = None
        self.stream_output: Optional[StreamResponse] = None

        # Get configuration
        llm_config = BrowserUseConfig.get_llm_config(llm_provider)
        browser_config = BrowserUseConfig.get_browser_config(headless)

        # Validate environment
        if not BrowserUseConfig.validate_environment(llm_config["provider"]):
            raise ValueError(f"Missing API key for {llm_config['provider']}")

        # Initialize LLM based on provider
        model = model or llm_config["model"]
        if llm_config["provider"] == "openai":
            self.llm = ChatOpenAI(model=model)
        elif llm_config["provider"] == "anthropic":
            self.llm = ChatAnthropic(model=model)
        else:
            raise ValueError(f"Unsupported LLM provider: {llm_config['provider']}")

        # Browser configuration
        self.headless = browser_config["headless"]
        self.use_vision = use_vision if use_vision is not None else browser_config["use_vision"]
        self.viewport = browser_config["viewport"]
        self.max_steps = browser_config["max_steps"]
        self.browser_session = None
        
    async def _initialize_browser_session(self) -> BrowserSession:
        """Initialize and return a browser session."""
        if self.browser_session is None:
            self.browser_session = BrowserSession(
                headless=self.headless,
                viewport=self.viewport,
                # Add user data directory for persistence if needed
                # user_data_dir='~/.config/browseruse/profiles/default',
            )
            await self.browser_session.start()
        return self.browser_session
    
    async def _send_step_update(self, message: str):
        """Send a step update via websocket if available."""
        if self.stream_output and self.websocket:
            self.stream_output.steps.append(message)
            await self.websocket.send_text(json.dumps(asdict(self.stream_output)))
            logfire.debug(f"WebSocket step update sent: {message}")
    
    async def generate_reply(
        self, instruction: str, websocket: WebSocket, stream_output: StreamResponse
    ) -> Tuple[bool, str, List]:
        """
        Generate a reply based on the instruction using browser-use.
        
        Args:
            instruction: The task instruction for the browser agent
            websocket: WebSocket connection for streaming updates
            stream_output: Stream response object for tracking progress
            
        Returns:
            Tuple of (success, result_message, pydantic_ai_messages)
        """
        try:
            logfire.debug(f"Starting browser-use task: {instruction}")
            self.websocket = websocket
            self.stream_output = stream_output
            
            # Initialize browser session
            await self._send_step_update("Initializing browser session...")
            browser_session = await self._initialize_browser_session()
            
            # Create browser-use agent with custom hooks for progress tracking
            await self._send_step_update("Creating AI browser agent...")
            
            async def step_hook(agent):
                """Hook to track agent steps and send updates."""
                current_url = (await agent.browser_session.get_current_page()).url
                step_count = len(agent.state.history.model_actions())
                await self._send_step_update(f"Step {step_count}: Navigating {current_url}")
            
            browser_agent = BrowserUseAgent(
                task=instruction,
                llm=self.llm,
                browser_session=browser_session,
                use_vision=self.use_vision,
                # Save conversation for debugging if needed
                # save_conversation_path="logs/browser_conversations"
            )
            
            await self._send_step_update("Starting browser automation task...")
            
            # Run the browser agent with step tracking
            history = await browser_agent.run(
                on_step_start=step_hook,
                max_steps=self.max_steps  # Use configured max steps
            )
            
            # Extract final result
            final_result = history.final_result()
            if not final_result:
                final_result = "Task completed but no specific result was extracted."
            
            # Check if task was successful
            success = history.is_done() and not history.has_errors()
            
            if success:
                await self._send_step_update("Browser task completed successfully")
                logfire.info(f"Browser-use task completed successfully: {instruction}")
            else:
                await self._send_step_update("Browser task completed with issues")
                logfire.warning(f"Browser-use task had issues: {instruction}")
            
            # Generate unique tool call ID and timestamp
            tool_call_id = f"call_{uuid.uuid4().hex[:24]}"
            current_time = datetime.utcnow()
            
            # Create properly structured pydantic-ai message objects
            request = ModelRequest([UserPromptPart(content=instruction)])
            
            args_data = {
                "terminated": True,
                "dependencies": [],
                "content": final_result,
                "success": success,
                "urls_visited": history.urls() if hasattr(history, 'urls') else [],
                "steps_taken": len(history.model_actions()) if hasattr(history, 'model_actions') else 0
            }
            
            response = ModelResponse(
                [
                    ToolCallPart(
                        tool_name="browser_automation_result",
                        tool_call_id=tool_call_id,
                        args=ArgsJson(args_json=json.dumps(args_data)),
                    )
                ],
                timestamp=current_time,
            )
            
            tool_return = ModelRequest(
                [
                    ToolReturnPart(
                        tool_name="browser_automation_result",
                        tool_call_id=tool_call_id,
                        content="Browser automation completed.",
                    )
                ]
            )
            
            messages = [request, response, tool_return]
            return success, final_result, messages
            
        except Exception as e:
            error_message = f"Failed to execute browser-use task: {str(e)}"
            logfire.error(error_message, exc_info=True)
            await self._send_step_update(f"Error: {error_message}")
            
            # Create error message as a proper ModelResponse
            error_response = ModelResponse(
                [
                    ToolCallPart(
                        tool_name="browser_automation_error",
                        tool_call_id=f"error_{uuid.uuid4().hex[:12]}",
                        args=ArgsJson(args_json=json.dumps({"error": error_message})),
                    )
                ],
                timestamp=datetime.utcnow(),
            )
            
            return False, error_message, [error_response]
    
    async def cleanup(self):
        """Clean up browser session resources."""
        if self.browser_session:
            try:
                await self.browser_session.close()
                logfire.debug("Browser session cleaned up successfully")
            except Exception as e:
                logfire.warning(f"Error during browser session cleanup: {e}")
            finally:
                self.browser_session = None


# Backward compatibility alias
WebSurfer = BrowserUseWebSurfer

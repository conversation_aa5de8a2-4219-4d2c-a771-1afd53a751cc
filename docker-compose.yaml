version: '3.8'

services:
  cortex_on:
    build:
      context: ./cortex_on
      dockerfile: Dockerfile
    volumes:
      - ./cortex_on:/app
    env_file:
      - .env
    restart: always
    ports:
      - "8081:8081"

  # Legacy browser service - now optional since cortex_on uses browser-use
  agentic_browser:
    build:
      context: ./ta-browser
      dockerfile: Dockerfile
    volumes:
      - ./ta-browser:/app
    env_file:
      - .env
    restart: always
    ports:
      - "8000:8000"
    profiles:
      - legacy  # Use 'docker-compose --profile legacy up' to include this service

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    volumes:
      - ./frontend:/app
      - /app/node_modules
    env_file:
      - .env
    depends_on:
      - cortex_on
      # Removed agentic_browser dependency - cortex_on now uses browser-use
    restart: always
    ports:
      - "3000:3000"
